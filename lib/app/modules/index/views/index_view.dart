import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controllers/index_controller.dart';
import '../../home/<USER>/home_view.dart';
import '../../orders/views/orders_view.dart';
import '../../analysis/views/analysis_view.dart';
import '../../categories/views/categories_view.dart';
import '../../settings/views/settings_view.dart';

class IndexView extends GetView<IndexController> {
  const IndexView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: Obx(() => _getCurrentPage()),
      bottomNavigationBar: Obx(() => BottomNavigationBar(
            type: BottomNavigationBarType.fixed,
            currentIndex: controller.currentTab.index,
            onTap: (index) => controller.changeTab(TabIndex.values[index]),
            backgroundColor: theme.colorScheme.surface,
            selectedItemColor: theme.colorScheme.primary,
            unselectedItemColor: theme.colorScheme.onSurface.withOpacity(0.6),
            selectedFontSize: 10,
            unselectedFontSize: 10,
            iconSize: 24,
            elevation: 8,
            items: [
              BottomNavigationBarItem(
                icon: const Icon(Icons.home),
                label: 'nav_home'.tr,
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.list),
                label: 'nav_transactions'.tr,
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.bar_chart),
                label: 'nav_analysis'.tr,
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.category),
                label: 'nav_categories'.tr,
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.settings),
                label: 'nav_settings'.tr,
              ),
            ],
          )),
    );
  }

  Widget _getCurrentPage() {
    switch (controller.currentTab) {
      case TabIndex.home:
        return const HomeView();
      case TabIndex.orders:
        return const OrdersView();
      case TabIndex.analysis:
        return const AnalysisView();
      case TabIndex.categories:
        return const CategoriesView();
      case TabIndex.settings:
        return const SettingsView();
    }
  }
}
