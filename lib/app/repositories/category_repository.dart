import 'package:objectbox/objectbox.dart';
import 'package:uuid/uuid.dart';

import '../../objectbox.g.dart';
import '../models/erp_category.dart';

class CategoryRepository {
  final Store _store;
  final Box<ErpCategory> _box;

  CategoryRepository(this._store) : _box = Box<ErpCategory>(_store);

  // 獲取所有類別
  List<ErpCategory> getAll({bool includeDeleted = false}) {
    if (!includeDeleted) {
      // 僅返回未刪除的類別
      final query = _box.query(ErpCategory_.deletedAt.isNull()).build();
      return query.find();
    } else {
      // 返回所有類別，包括已刪除的
      return _box.getAll();
    }
  }

  // 按ID獲取類別
  ErpCategory? getById(int id) {
    return _box.get(id);
  }

  // 按 UUID 獲取類別
  ErpCategory? getByUuid(String uuid) {
    final query = _box.query(ErpCategory_.uuid.equals(uuid)).build();
    final results = query.find();
    query.close();
    return results.isNotEmpty ? results.first : null;
  }

  // 按名稱搜尋類別
  List<ErpCategory> searchByName(String name) {
    final query = _box
        .query(
            ErpCategory_.name.contains(name) & ErpCategory_.deletedAt.isNull())
        .build();
    final results = query.find();
    query.close();
    return results;
  }

  // 新增類別
  int add(ErpCategory category) {
    // 設置創建時間和UUID（如果未提供）
    final now = DateTime.now();
    category.createdAt ??= now;
    category.updatedAt = now;
    category.uuid ??= const Uuid().v4();

    return _box.put(category);
  }

  // 更新類別
  int update(ErpCategory category) {
    // 確保更新時間已設置
    category.updatedAt = DateTime.now();

    return _box.put(category);
  }

  // 批量添加或更新類別
  List<int> putMany(List<ErpCategory> categories) {
    final now = DateTime.now();
    for (final category in categories) {
      // 如果是新物件，設置創建時間和UUID
      if (category.id == null) {
        category.createdAt ??= now;
        category.uuid ??= const Uuid().v4();
      }
      category.updatedAt = now;
    }

    return _box.putMany(categories);
  }

  // 軟刪除類別（設置刪除時間而不是實際刪除）
  bool softDelete(int id) {
    final category = _box.get(id);
    if (category != null) {
      category.deletedAt = DateTime.now();
      _box.put(category);
      return true;
    }
    return false;
  }

  // 恢復已軟刪除的類別
  bool restore(int id) {
    final category = _box.get(id);
    if (category != null && category.deletedAt != null) {
      category.deletedAt = null;
      _box.put(category);
      return true;
    }
    return false;
  }

  // 硬刪除類別（真正從資料庫中刪除）
  bool hardDelete(int id) {
    return _box.remove(id);
  }

  // 刪除所有類別
  int deleteAll() {
    return _box.removeAll();
  }

  // 獲取已排序的類別列表（按照sort字段排序）
  List<ErpCategory> getAllSorted({bool ascending = true}) {
    final queryBuilder = _box.query(ErpCategory_.deletedAt.isNull());

    if (ascending) {
      queryBuilder.order(ErpCategory_.sort);
    } else {
      queryBuilder.order(ErpCategory_.sort, flags: Order.descending);
    }

    final query = queryBuilder.build();
    final results = query.find();
    query.close();
    return results;
  }

  // 獲取類別總數
  int count({bool includeDeleted = false}) {
    if (!includeDeleted) {
      return _box.query(ErpCategory_.deletedAt.isNull()).build().count();
    } else {
      return _box.count();
    }
  }
}
