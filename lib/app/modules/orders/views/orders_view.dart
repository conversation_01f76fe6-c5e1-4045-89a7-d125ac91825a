import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/orders_controller.dart';

class OrdersView extends GetView<OrdersController> {
  const OrdersView({super.key});
  @override
  Widget build(BuildContext context) {
    Get.lazyPut<OrdersController>(
      () => OrdersController(
        prefProvider: Get.find(),
      ),
    );
    controller.talker.debug('OrdersView build');
    return Scaffold(
      appBar: AppBar(
        title: const Text('OrdersView'),
        centerTitle: true,
      ),
      body: controller.obx(
        (state) {
          return const Center(
            child: Text(
              'OrdersView is working',
              style: TextStyle(fontSize: 20),
            ),
          );
        },
        onLoading: const Center(
          child: CircularProgressIndicator(),
        ),
        onError: (error) => Center(
          child: Text(error.toString()),
        ),
      ),
    );
  }
}
