// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again
// with `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'
    as obx_int; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart' as obx;

import 'app/models/erp_category.dart';
import 'app/models/erp_order.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <obx_int.ModelEntity>[
  obx_int.ModelEntity(
      id: const obx_int.IdUid(1, 4736524990562452743),
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      lastPropertyId: const obx_int.IdUid(8, 5926307986073563950),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 6699010992199739088),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 9095140455919509515),
            name: 'createdAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 1645709725668348699),
            name: 'updatedAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 4211576626160417903),
            name: 'deletedAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 8550521329758439397),
            name: 'sort',
            type: 6,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 5557794864216679716),
            name: 'name',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 4575044179609350900),
            name: 'color',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 5926307986073563950),
            name: 'uuid',
            type: 9,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[
        obx_int.ModelBacklink(
            name: 'children', srcEntity: 'ErpOrder', srcField: '')
      ]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(2, 3383603721258941482),
      name: 'ErpOrder',
      lastPropertyId: const obx_int.IdUid(13, 1976928675019386969),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 2459716785988597027),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 436149355993711931),
            name: 'createdAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 8911549518193149301),
            name: 'updatedAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 2914967930156451877),
            name: 'deletedAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 2190182430680927835),
            name: 'triggerAt',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 5366519926545347263),
            name: 'type',
            type: 6,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 7329675281810433436),
            name: 'amount',
            type: 8,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 4001369780538854101),
            name: 'note',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(9, 2182345955024867809),
            name: 'latitude',
            type: 8,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(10, 8663974601994510231),
            name: 'longitude',
            type: 8,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(11, 2875396538869356295),
            name: 'parentTable',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(12, 916747874553523198),
            name: 'parentId',
            type: 11,
            flags: 520,
            indexId: const obx_int.IdUid(1, 3235960189220155324),
            relationTarget: 'ErpCategory'),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(13, 1976928675019386969),
            name: 'uuid',
            type: 9,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[])
];

/// Shortcut for [obx.Store.new] that passes [getObjectBoxModel] and for Flutter
/// apps by default a [directory] using `defaultStoreDirectory()` from the
/// ObjectBox Flutter library.
///
/// Note: for desktop apps it is recommended to specify a unique [directory].
///
/// See [obx.Store.new] for an explanation of all parameters.
///
/// For Flutter apps, also calls `loadObjectBoxLibraryAndroidCompat()` from
/// the ObjectBox Flutter library to fix loading the native ObjectBox library
/// on Android 6 and older.
obx.Store openStore(
    {String? directory,
    int? maxDBSizeInKB,
    int? maxDataSizeInKB,
    int? fileMode,
    int? maxReaders,
    bool queriesCaseSensitiveDefault = true,
    String? macosApplicationGroup}) {
  return obx.Store(getObjectBoxModel(),
      directory: directory,
      maxDBSizeInKB: maxDBSizeInKB,
      maxDataSizeInKB: maxDataSizeInKB,
      fileMode: fileMode,
      maxReaders: maxReaders,
      queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
      macosApplicationGroup: macosApplicationGroup);
}

/// Returns the ObjectBox model definition for this project for use with
/// [obx.Store.new].
obx_int.ModelDefinition getObjectBoxModel() {
  final model = obx_int.ModelInfo(
      entities: _entities,
      lastEntityId: const obx_int.IdUid(2, 3383603721258941482),
      lastIndexId: const obx_int.IdUid(1, 3235960189220155324),
      lastRelationId: const obx_int.IdUid(0, 0),
      lastSequenceId: const obx_int.IdUid(0, 0),
      retiredEntityUids: const [],
      retiredIndexUids: const [],
      retiredPropertyUids: const [],
      retiredRelationUids: const [],
      modelVersion: 5,
      modelVersionParserMinimum: 5,
      version: 1);

  final bindings = <Type, obx_int.EntityDefinition>{
    ErpCategory: obx_int.EntityDefinition<ErpCategory>(
        model: _entities[0],
        toOneRelations: (ErpCategory object) => [],
        toManyRelations: (ErpCategory object) => {
              obx_int.RelInfo<ErpOrder>.toOneBacklink(
                      12, object.id!, (ErpOrder srcObject) => srcObject.parent):
                  object.children
            },
        getId: (ErpCategory object) => object.id,
        setId: (ErpCategory object, int id) {
          object.id = id;
        },
        objectToFB: (ErpCategory object, fb.Builder fbb) {
          final nameOffset =
              object.name == null ? null : fbb.writeString(object.name!);
          final colorOffset =
              object.color == null ? null : fbb.writeString(object.color!);
          final uuidOffset =
              object.uuid == null ? null : fbb.writeString(object.uuid!);
          fbb.startTable(9);
          fbb.addInt64(0, object.id ?? 0);
          fbb.addInt64(1, object.createdAt?.millisecondsSinceEpoch);
          fbb.addInt64(2, object.updatedAt?.millisecondsSinceEpoch);
          fbb.addInt64(3, object.deletedAt?.millisecondsSinceEpoch);
          fbb.addInt64(4, object.sort);
          fbb.addOffset(5, nameOffset);
          fbb.addOffset(6, colorOffset);
          fbb.addOffset(7, uuidOffset);
          fbb.finish(fbb.endTable());
          return object.id ?? 0;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final createdAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 6);
          final updatedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 8);
          final deletedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 10);
          final idParam =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 4);
          final createdAtParam = createdAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(createdAtValue);
          final updatedAtParam = updatedAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(updatedAtValue);
          final deletedAtParam = deletedAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(deletedAtValue);
          final sortParam =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 12);
          final nameParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 14);
          final uuidParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 18);
          final colorParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 16);
          final object = ErpCategory(
              id: idParam,
              createdAt: createdAtParam,
              updatedAt: updatedAtParam,
              deletedAt: deletedAtParam,
              sort: sortParam,
              name: nameParam,
              uuid: uuidParam,
              color: colorParam);
          obx_int.InternalToManyAccess.setRelInfo<ErpCategory>(
              object.children,
              store,
              obx_int.RelInfo<ErpOrder>.toOneBacklink(
                  12, object.id!, (ErpOrder srcObject) => srcObject.parent));
          return object;
        }),
    ErpOrder: obx_int.EntityDefinition<ErpOrder>(
        model: _entities[1],
        toOneRelations: (ErpOrder object) => [object.parent],
        toManyRelations: (ErpOrder object) => {},
        getId: (ErpOrder object) => object.id,
        setId: (ErpOrder object, int id) {
          object.id = id;
        },
        objectToFB: (ErpOrder object, fb.Builder fbb) {
          final noteOffset =
              object.note == null ? null : fbb.writeString(object.note!);
          final parentTableOffset = object.parentTable == null
              ? null
              : fbb.writeString(object.parentTable!);
          final uuidOffset =
              object.uuid == null ? null : fbb.writeString(object.uuid!);
          fbb.startTable(14);
          fbb.addInt64(0, object.id ?? 0);
          fbb.addInt64(1, object.createdAt?.millisecondsSinceEpoch);
          fbb.addInt64(2, object.updatedAt?.millisecondsSinceEpoch);
          fbb.addInt64(3, object.deletedAt?.millisecondsSinceEpoch);
          fbb.addInt64(4, object.triggerAt?.millisecondsSinceEpoch);
          fbb.addInt64(5, object.type);
          fbb.addFloat64(6, object.amount);
          fbb.addOffset(7, noteOffset);
          fbb.addFloat64(8, object.latitude);
          fbb.addFloat64(9, object.longitude);
          fbb.addOffset(10, parentTableOffset);
          fbb.addInt64(11, object.parent.targetId);
          fbb.addOffset(12, uuidOffset);
          fbb.finish(fbb.endTable());
          return object.id ?? 0;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final createdAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 6);
          final updatedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 8);
          final deletedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 10);
          final triggerAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 12);
          final idParam =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 4);
          final createdAtParam = createdAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(createdAtValue);
          final updatedAtParam = updatedAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(updatedAtValue);
          final deletedAtParam = deletedAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(deletedAtValue);
          final triggerAtParam = triggerAtValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(triggerAtValue);
          final latitudeParam = const fb.Float64Reader()
              .vTableGetNullable(buffer, rootOffset, 20);
          final longitudeParam = const fb.Float64Reader()
              .vTableGetNullable(buffer, rootOffset, 22);
          final typeParam =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 14);
          final amountParam = const fb.Float64Reader()
              .vTableGetNullable(buffer, rootOffset, 16);
          final noteParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 18);
          final parentTableParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 24);
          final uuidParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 28);
          final object = ErpOrder(
              id: idParam,
              createdAt: createdAtParam,
              updatedAt: updatedAtParam,
              deletedAt: deletedAtParam,
              triggerAt: triggerAtParam,
              latitude: latitudeParam,
              longitude: longitudeParam,
              type: typeParam,
              amount: amountParam,
              note: noteParam,
              parentTable: parentTableParam,
              uuid: uuidParam);
          object.parent.targetId =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 26, 0);
          object.parent.attach(store);
          return object;
        })
  };

  return obx_int.ModelDefinition(model, bindings);
}

/// [ErpCategory] entity fields to define ObjectBox queries.
class ErpCategory_ {
  /// See [ErpCategory.id].
  static final id =
      obx.QueryIntegerProperty<ErpCategory>(_entities[0].properties[0]);

  /// See [ErpCategory.createdAt].
  static final createdAt =
      obx.QueryDateProperty<ErpCategory>(_entities[0].properties[1]);

  /// See [ErpCategory.updatedAt].
  static final updatedAt =
      obx.QueryDateProperty<ErpCategory>(_entities[0].properties[2]);

  /// See [ErpCategory.deletedAt].
  static final deletedAt =
      obx.QueryDateProperty<ErpCategory>(_entities[0].properties[3]);

  /// See [ErpCategory.sort].
  static final sort =
      obx.QueryIntegerProperty<ErpCategory>(_entities[0].properties[4]);

  /// See [ErpCategory.name].
  static final name =
      obx.QueryStringProperty<ErpCategory>(_entities[0].properties[5]);

  /// See [ErpCategory.color].
  static final color =
      obx.QueryStringProperty<ErpCategory>(_entities[0].properties[6]);

  /// See [ErpCategory.uuid].
  static final uuid =
      obx.QueryStringProperty<ErpCategory>(_entities[0].properties[7]);

  /// see [ErpCategory.children]
  static final children =
      obx.QueryBacklinkToMany<ErpOrder, ErpCategory>(ErpOrder_.parent);
}

/// [ErpOrder] entity fields to define ObjectBox queries.
class ErpOrder_ {
  /// See [ErpOrder.id].
  static final id =
      obx.QueryIntegerProperty<ErpOrder>(_entities[1].properties[0]);

  /// See [ErpOrder.createdAt].
  static final createdAt =
      obx.QueryDateProperty<ErpOrder>(_entities[1].properties[1]);

  /// See [ErpOrder.updatedAt].
  static final updatedAt =
      obx.QueryDateProperty<ErpOrder>(_entities[1].properties[2]);

  /// See [ErpOrder.deletedAt].
  static final deletedAt =
      obx.QueryDateProperty<ErpOrder>(_entities[1].properties[3]);

  /// See [ErpOrder.triggerAt].
  static final triggerAt =
      obx.QueryDateProperty<ErpOrder>(_entities[1].properties[4]);

  /// See [ErpOrder.type].
  static final type =
      obx.QueryIntegerProperty<ErpOrder>(_entities[1].properties[5]);

  /// See [ErpOrder.amount].
  static final amount =
      obx.QueryDoubleProperty<ErpOrder>(_entities[1].properties[6]);

  /// See [ErpOrder.note].
  static final note =
      obx.QueryStringProperty<ErpOrder>(_entities[1].properties[7]);

  /// See [ErpOrder.latitude].
  static final latitude =
      obx.QueryDoubleProperty<ErpOrder>(_entities[1].properties[8]);

  /// See [ErpOrder.longitude].
  static final longitude =
      obx.QueryDoubleProperty<ErpOrder>(_entities[1].properties[9]);

  /// See [ErpOrder.parentTable].
  static final parentTable =
      obx.QueryStringProperty<ErpOrder>(_entities[1].properties[10]);

  /// See [ErpOrder.parent].
  static final parent = obx.QueryRelationToOne<ErpOrder, ErpCategory>(
      _entities[1].properties[11]);

  /// See [ErpOrder.uuid].
  static final uuid =
      obx.QueryStringProperty<ErpOrder>(_entities[1].properties[12]);
}
