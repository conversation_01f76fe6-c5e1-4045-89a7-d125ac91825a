import 'dart:developer';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:pocket_trac/app/extensions/box_provider_extension.dart';
import 'package:talker_flutter/talker_flutter.dart';

import 'app/providers/box_provider.dart';
import 'app/providers/pref_provider.dart';
import 'app/routes/app_pages.dart';
import 'app/translations/app_translations.dart';
import 'colors.dart';

void main() {
  // it should be the first line in main method
  WidgetsFlutterBinding.ensureInitialized();
  // await service initialized
  _init().then((value) => _run()).catchError((error) => log(error));
}

void _run() {
  final prefProvider = Get.find<PrefProvider>();

  runApp(Obx(() {
    return GetMaterialApp(
      title: 'app_title'.tr,
      initialRoute: AppPages.INITIAL,
      getPages: AppPages.routes,
      translations: AppTranslations(),
      locale: prefProvider.currentLocale,
      fallbackLocale: const Locale('zh', 'TW'),
      supportedLocales: prefProvider.supportedLocales,
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      theme: ErpColors.lightTheme,
      darkTheme: ErpColors.darkTheme,
      themeMode: prefProvider.currentThemeMode,
    );
  }));
}

Future<void> _init() async {
  // init talker
  Get.lazyPut(
    () => TalkerFlutter.init(
      observer: CrashlyticsTalkerObserver(),
    ),
    fenix: true,
  );
  // box provider
  final boxProvider = Get.put(
    BoxProvider(talker: Get.find()),
    permanent: true,
  );
  await boxProvider.initBoxes();
  // package info
  await Get.putAsync(
    () => PackageInfo.fromPlatform(),
    permanent: true,
  );
  // device info
  Get.lazyPut(
    () => DeviceInfoPlugin(),
    fenix: true,
  );
  // pref provider
  Get.lazyPut(
    () => PrefProvider(
      boxProvider: Get.find(),
      deviceInfo: Get.find(),
      packageInfo: Get.find(),
    ),
    fenix: true,
  );
}

class CrashlyticsTalkerObserver extends TalkerObserver {
  CrashlyticsTalkerObserver();

  @override
  void onError(err) {
    FirebaseCrashlytics.instance.recordError(
      err.error,
      err.stackTrace,
      reason: err.message,
    );
  }

  @override
  void onException(err) {
    FirebaseCrashlytics.instance.recordError(
      err.exception,
      err.stackTrace,
      reason: err.message,
    );
  }
}
