import 'dart:convert';

import 'package:objectbox/objectbox.dart';

import 'erp_category.dart';

@Entity()
class ErpOrder {
  @Id()
  int? id;
  @Property(type: PropertyType.date) // Store as int in milliseconds
  DateTime? createdAt;
  @Property(type: PropertyType.date) // Store as int in milliseconds
  DateTime? updatedAt;
  @Property(type: PropertyType.date) // Store as int in milliseconds
  DateTime? deletedAt;
  @Property(type: PropertyType.date) // Store as int in milliseconds
  DateTime? triggerAt;
  int? type;
  double? amount;
  String? note;
  double? latitude;
  double? longitude;
  String? parentTable;
  int? parentId;
  String? uuid;
  final parent = ToOne<ErpCategory>();

  ErpOrder({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.triggerAt,
    this.latitude,
    this.longitude,
    this.type,
    this.amount,
    this.note,
    this.parentTable,
    this.parentId,
    this.uuid,
  });

  ErpOrder copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    DateTime? triggerAt,
    double? latitude,
    double? longitude,
    int? type,
    double? amount,
    String? note,
    int? parentId,
    String? parentTable,
    String? uuid,
  }) =>
      ErpOrder(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        triggerAt: triggerAt ?? this.triggerAt,
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        type: type ?? this.type,
        amount: amount ?? this.amount,
        note: note ?? this.note,
        parentTable: parentTable ?? this.parentTable,
        parentId: parentId ?? this.parentId,
        uuid: uuid ?? this.uuid,
      );

  factory ErpOrder.fromRawJson(String str) =>
      ErpOrder.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ErpOrder.fromJson(Map<String, dynamic> json) => ErpOrder(
        id: json["id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        deletedAt: json["deleted_at"] == null
            ? null
            : DateTime.parse(json["deleted_at"]),
        triggerAt: json["trigger_at"] == null
            ? null
            : DateTime.parse(json["trigger_at"]),
        type: json["type"],
        amount: json["amount"]?.toDouble(),
        note: json["note"],
        latitude: json["latitude"]?.toDouble(),
        longitude: json["longitude"]?.toDouble(),
        parentTable: json["parent_table"],
        parentId: json["parent_id"],
        uuid: json["uuid"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "deleted_at": deletedAt?.toIso8601String(),
        "trigger_at": triggerAt?.toIso8601String(),
        "type": type,
        "amount": amount,
        "note": note,
        "latitude": latitude,
        "longitude": longitude,
        "parent_table": parentTable,
        "parent_id": parentId,
        "uuid": uuid,
      };
}
