import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

extension WidgetX on Widget {
  // 可以把任何 Widget 使用 Dialog 形式呈現
  Future<T?> dialog<T>({
    bool barrierDismissible = true,
    EdgeInsets insetPadding = EdgeInsets.zero,
  }) {
    return Get.dialog<T>(
      Dialog(
        insetPadding: insetPadding,
        backgroundColor: Colors.transparent,
        elevation: 0.0,
        child: SizedBox(
          // width: 300.dw,
          child: this,
        ),
      ),
      barrierDismissible: barrierDismissible,
    );
  }

  // 可以把任何 Widget 使用 Sheet 形式呈現
  Future<T?> sheet<T>({
    final bool isDismissible = true,
    final bool enableDrag = false,
    final bool isScrollControlled = true,
    final bool ignoreSafeArea = false,
  }) {
    return Get.bottomSheet<T>(
      SafeArea(child: this),
      isScrollControlled: isScrollControlled,
      enableDrag: enableDrag,
      ignoreSafeArea: ignoreSafeArea,
      isDismissible: isDismissible,
      enterBottomSheetDuration: 200.milliseconds,
      exitBottomSheetDuration: 200.milliseconds,
    );
  }
}

extension ThemeModeX on ThemeMode {
  String get display {
    switch (this) {
      case ThemeMode.light:
        return 'theme_light'.tr;
      case ThemeMode.dark:
        return 'theme_dark'.tr;
      case ThemeMode.system:
        return 'theme_system'.tr;
      default:
        return '';
    }
  }
}

extension LocaleX on Locale {
  String get display {
    switch ('${languageCode}_$countryCode') {
      case 'en_US':
        return 'language_english'.tr;
      case 'zh_TW':
        return 'language_chinese'.tr;
      default:
        return 'language_chinese'.tr;
    }
  }

  String get flagEmoji {
    switch ('${languageCode}_$countryCode') {
      case 'en_US':
        return '🇺🇸';
      case 'zh_TW':
        return '🇹🇼';
      default:
        return '🇹🇼';
    }
  }
}

extension GetStorageX on GetStorage {
  Stream<String> watch([String? key]) async* {
    late final StreamController<String> streamController;
    late final VoidCallback disposeListen;

    try {
      streamController = StreamController<String>();

      if (key != null && key.isNotEmpty) {
        disposeListen = listenKey(key, (value) {
          if (!streamController.isClosed) {
            streamController.add(value ?? '');
          }
        });
      } else {
        disposeListen = listen(() {
          if (!streamController.isClosed) {
            streamController.add('');
          }
        });
      }

      // 發出初始值
      yield key ?? '';

      // 持續監聽變更
      await for (final value in streamController.stream) {
        yield value;
      }
    } finally {
      // 確保資源被正確釋放
      disposeListen.call();
      if (!streamController.isClosed) {
        streamController.close();
      }
    }
  }
}
