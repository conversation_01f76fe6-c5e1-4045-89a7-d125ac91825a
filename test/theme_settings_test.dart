import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pocket_trac/colors.dart';
import 'package:pocket_trac/extension.dart';

void main() {
  group('Theme Settings Tests', () {
    setUp(() async {
      // 初始化 Flutter 绑定
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    test('ThemeMode扩展应该正确显示', () {
      expect(ThemeMode.light.display, '使用淺色主題');
      expect(ThemeMode.dark.display, '使用深色主題');
      expect(ThemeMode.system.display, '跟隨系統設定');
    });

    test('主题配置应该正确', () {
      // 测试浅色主题配置
      final lightTheme = ErpColors.lightTheme;
      expect(lightTheme.brightness, Brightness.light);
      expect(lightTheme.primaryColor, ErpColors.primary);
      expect(lightTheme.scaffoldBackgroundColor, ErpColors.background);

      // 测试深色主题配置
      final darkTheme = ErpColors.darkTheme;
      expect(darkTheme.brightness, Brightness.dark);
      expect(darkTheme.primaryColor, ErpColors.primary);
      expect(darkTheme.scaffoldBackgroundColor, ErpColors.darkBackground);
    });

    test('主题颜色应该不同', () {
      final lightTheme = ErpColors.lightTheme;
      final darkTheme = ErpColors.darkTheme;

      // 背景颜色应该不同
      expect(lightTheme.scaffoldBackgroundColor, isNot(darkTheme.scaffoldBackgroundColor));

      // 卡片颜色应该不同
      expect(lightTheme.cardColor, isNot(darkTheme.cardColor));

      // 分隔线颜色应该不同
      expect(lightTheme.dividerColor, isNot(darkTheme.dividerColor));
    });

    test('颜色常量应该正确定义', () {
      // 测试主要颜色
      expect(ErpColors.primary, const Color(0xFF3B82F6));
      expect(ErpColors.primaryLight, const Color(0xFF60A5FA));
      expect(ErpColors.primaryDark, const Color(0xFF1D4ED8));

      // 测试深色主题颜色
      expect(ErpColors.darkBackground, const Color(0xFF121212));
      expect(ErpColors.darkSurface, const Color(0xFF1E1E1E));
      expect(ErpColors.darkCardBackground, const Color(0xFF2D2D2D));

      // 测试渐变色
      expect(ErpColors.gradientStart, const Color(0xFF667EEA));
      expect(ErpColors.gradientEnd, const Color(0xFF764BA2));
      expect(ErpColors.darkGradientStart, const Color(0xFF2D3748));
      expect(ErpColors.darkGradientEnd, const Color(0xFF4A5568));
    });

    test('状态颜色应该正确定义', () {
      expect(ErpColors.success, const Color(0xFF10B981));
      expect(ErpColors.warning, const Color(0xFFF59E0B));
      expect(ErpColors.error, const Color(0xFFEF4444));
      expect(ErpColors.info, const Color(0xFF3B82F6));
    });
  });
}
