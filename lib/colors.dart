import 'package:flutter/material.dart';

class ErpColors {
  // Primary gradient colors (from prototype design)
  static const Color gradientStart =
      Color(0xFF667EEA); // Light blue-purple #667eea
  static const Color gradientEnd = Color(0xFF764BA2); // Deep purple #764ba2

  // Primary colors (updated to match modern design)
  static const Color primary =
      Color(0xFF3B82F6); // Blue-500 (primary action color)
  static const Color primaryLight = Color(0xFF60A5FA); // Blue-400
  static const Color primaryDark = Color(0xFF1D4ED8); // Blue-700

  // Accent colors (purple theme from gradient)
  static const Color accent = Color(0xFF8B5CF6); // Purple-500
  static const Color accentLight = Color(0xFFA78BFA); // Purple-400
  static const Color accentDark = Color(0xFF7C3AED); // Purple-600

  // Text colors
  static const Color textPrimary =
      Color(0xFF1F2937); // Gray-800 (darker for better contrast)
  static const Color textSecondary = Color(0xFF6B7280); // Gray-500
  static const Color textHint = Color(0xFF9CA3AF); // Gray-400
  static const Color textWhite =
      Color(0xFFFFFFFF); // White text for dark backgrounds

  // Background colors
  static const Color background = Color(0xFFFFFFFF); // Pure white
  static const Color backgroundLight = Color(0xFFF9FAFB); // Gray-50
  static const Color backgroundDark = Color(0xFFF3F4F6); // Gray-100
  static const Color cardBackground =
      Color(0xFFFFFFFF); // White cards with shadow

  // Overlay colors (for glassmorphism effects like in prototype)
  static const Color overlayLight = Color(0x1AFFFFFF); // 10% white
  static const Color overlayMedium = Color(0x33FFFFFF); // 20% white
  static const Color overlayStrong = Color(0x4DFFFFFF); // 30% white

  // Status colors (updated to match prototype)
  static const Color success = Color(0xFF10B981); // Green-500 (Emerald)
  static const Color successLight = Color(0xFF6EE7B7); // Green-300
  static const Color warning = Color(0xFFF59E0B); // Amber-500
  static const Color warningLight = Color(0xFFFCD34D); // Yellow-300
  static const Color error = Color(0xFFEF4444); // Red-500
  static const Color errorLight = Color(0xFFFCA5A5); // Red-300
  static const Color info = Color(0xFF3B82F6); // Blue-500

  // Income/Expense colors (matching prototype)
  static const Color income = Color(0xFF10B981); // Green-500 for income
  static const Color incomeLight =
      Color(0xFF6EE7B7); // Green-300 (as used in prototype)
  static const Color expense = Color(0xFFEF4444); // Red-500 for expense
  static const Color expenseLight =
      Color(0xFFFCA5A5); // Red-300 (as used in prototype)
  static const Color balance = Color(0xFFF59E0B); // Amber-500 for balance
  static const Color balanceLight =
      Color(0xFFFCD34D); // Yellow-300 (as used in prototype)

  // Category colors (from prototype pie chart)
  static const Color categoryFood = Color(0xFF3B82F6); // Blue-500 (餐飲)
  static const Color categoryTransport = Color(0xFF8B5CF6); // Purple-500 (交通)
  static const Color categoryShopping = Color(0xFFEC4899); // Pink-500 (購物)
  static const Color categoryOther = Color(0xFF10B981); // Green-500 (其他)

  // Additional category colors for expansion
  static const Color categoryEntertainment = Color(0xFFF59E0B); // Amber-500
  static const Color categoryHealth = Color(0xFF06B6D4); // Cyan-500
  static const Color categoryEducation = Color(0xFF8B5CF6); // Purple-500
  static const Color categoryUtilities = Color(0xFF6B7280); // Gray-500

  // UI element colors
  static const Color divider = Color(0xFFE5E7EB); // Gray-200
  static const Color disabled = Color(0xFF9CA3AF); // Gray-400
  static const Color shadow = Color(0x1F000000); // 12% black
  static const Color border = Color(0xFFE5E7EB); // Gray-200

  // Bottom navigation colors
  static const Color bottomNavBackground = Color(0xFFFFFFFF);
  static const Color bottomNavInactive = Color(0xFF9CA3AF); // Gray-400
  static const Color bottomNavActive = primary; // Blue-500

  // Icon background colors (for transaction icons)
  static const Color iconBackgroundRed = Color(0xFFFEE2E2); // Red-100
  static const Color iconBackgroundBlue = Color(0xFFDBEAFE); // Blue-100
  static const Color iconBackgroundGreen = Color(0xFFD1FAE5); // Green-100
  static const Color iconBackgroundPurple = Color(0xFFEDE9FE); // Purple-100
  static const Color iconBackgroundYellow = Color(0xFFFEF3C7); // Yellow-100
  static const Color iconBackgroundPink = Color(0xFFFCE7F3); // Pink-100

  // Dark theme colors
  static const Color darkBackground = Color(0xFF121212); // Dark background
  static const Color darkSurface = Color(0xFF1E1E1E); // Dark surface
  static const Color darkCardBackground = Color(0xFF2D2D2D); // Dark card background
  static const Color darkTextPrimary = Color(0xFFE0E0E0); // Light text for dark theme
  static const Color darkTextSecondary = Color(0xFFB0B0B0); // Secondary text for dark theme
  static const Color darkDivider = Color(0xFF404040); // Dark divider
  static const Color darkBorder = Color(0xFF404040); // Dark border

  // Dark theme gradient colors
  static const Color darkGradientStart = Color(0xFF2D3748); // Dark blue-gray
  static const Color darkGradientEnd = Color(0xFF4A5568); // Darker blue-gray

  /// 获取浅色主题配置
  static ThemeData get lightTheme {
    return ThemeData(
      brightness: Brightness.light,
      primaryColor: primary,
      primaryColorLight: primaryLight,
      primaryColorDark: primaryDark,
      colorScheme: ColorScheme.light(
        primary: primary,
        secondary: accent,
        error: error,
        surface: cardBackground,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: primary,
        foregroundColor: textWhite,
        elevation: 0,
      ),
      scaffoldBackgroundColor: background,
      cardColor: cardBackground,
      dividerColor: divider,
      textTheme: TextTheme(
        bodyLarge: TextStyle(color: textPrimary),
        bodyMedium: TextStyle(color: textSecondary),
      ),
    );
  }

  /// 获取深色主题配置
  static ThemeData get darkTheme {
    return ThemeData(
      brightness: Brightness.dark,
      primaryColor: primary,
      primaryColorLight: primaryLight,
      primaryColorDark: primaryDark,
      colorScheme: ColorScheme.dark(
        primary: primary,
        secondary: accent,
        error: error,
        surface: darkSurface,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: darkSurface,
        foregroundColor: darkTextPrimary,
        elevation: 0,
      ),
      scaffoldBackgroundColor: darkBackground,
      cardColor: darkCardBackground,
      dividerColor: darkDivider,
      textTheme: TextTheme(
        bodyLarge: TextStyle(color: darkTextPrimary),
        bodyMedium: TextStyle(color: darkTextSecondary),
      ),
    );
  }
}
