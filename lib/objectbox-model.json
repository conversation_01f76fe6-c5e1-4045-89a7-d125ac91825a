{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:4736524990562452743", "lastPropertyId": "8:5926307986073563950", "name": "ErpCategory", "properties": [{"id": "1:6699010992199739088", "name": "id", "type": 6, "flags": 1}, {"id": "2:9095140455919509515", "name": "createdAt", "type": 10}, {"id": "3:1645709725668348699", "name": "updatedAt", "type": 10}, {"id": "4:4211576626160417903", "name": "deletedAt", "type": 10}, {"id": "5:8550521329758439397", "name": "sort", "type": 6}, {"id": "6:5557794864216679716", "name": "name", "type": 9}, {"id": "7:4575044179609350900", "name": "color", "type": 9}, {"id": "8:5926307986073563950", "name": "uuid", "type": 9}], "relations": []}, {"id": "2:3383603721258941482", "lastPropertyId": "13:1976928675019386969", "name": "ErpOrder", "properties": [{"id": "1:2459716785988597027", "name": "id", "type": 6, "flags": 1}, {"id": "2:436149355993711931", "name": "createdAt", "type": 10}, {"id": "3:8911549518193149301", "name": "updatedAt", "type": 10}, {"id": "4:2914967930156451877", "name": "deletedAt", "type": 10}, {"id": "5:2190182430680927835", "name": "triggerAt", "type": 10}, {"id": "6:5366519926545347263", "name": "type", "type": 6}, {"id": "7:7329675281810433436", "name": "amount", "type": 8}, {"id": "8:4001369780538854101", "name": "note", "type": 9}, {"id": "9:2182345955024867809", "name": "latitude", "type": 8}, {"id": "10:8663974601994510231", "name": "longitude", "type": 8}, {"id": "11:2875396538869356295", "name": "parentTable", "type": 9}, {"id": "12:916747874553523198", "name": "parentId", "type": 11, "flags": 520, "indexId": "1:3235960189220155324", "relationTarget": "ErpCategory"}, {"id": "13:1976928675019386969", "name": "uuid", "type": 9}], "relations": []}], "lastEntityId": "2:3383603721258941482", "lastIndexId": "1:3235960189220155324", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [], "retiredRelationUids": [], "version": 1}