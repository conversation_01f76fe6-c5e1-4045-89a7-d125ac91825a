<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PocketTrac - 新增類別</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 0 auto;
            background: #000;
            border-radius: 25px;
            padding: 10px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
        }
        .color-option.selected {
            border-color: #ffffff;
            box-shadow: 0 0 0 2px #667eea;
        }
        .icon-option {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            border: 2px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.2s;
        }
        .icon-option.selected {
            border-color: #667eea;
            background-color: #eff6ff;
        }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="phone-container">
        <div class="phone-screen">
            <!-- Status Bar -->
            <div class="status-bar flex justify-between items-center px-4">
                <span class="text-sm font-medium">9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <i class="fas fa-battery-full text-xs"></i>
                </div>
            </div>

            <!-- Header -->
            <div class="gradient-bg text-white px-4 py-4">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <button class="text-white">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                        <h1 class="text-xl font-bold">新增類別</h1>
                    </div>
                    <button class="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-lg text-sm font-medium">
                        儲存
                    </button>
                </div>

                <!-- 預覽區 -->
                <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 mb-4">
                    <p class="text-white/80 text-sm mb-3">預覽</p>
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-utensils text-white text-lg"></i>
                        </div>
                        <div>
                            <p class="text-white font-medium text-lg">餐飲</p>
                            <p class="text-white/70 text-sm">支出類別</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Content -->
            <div class="flex-1 px-4 py-6 pb-20 overflow-y-auto">
                <!-- 類別名稱 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2">類別名稱</label>
                    <input type="text" placeholder="輸入類別名稱" value="餐飲"
                           class="w-full bg-white border border-gray-200 rounded-xl px-4 py-3 text-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <!-- 類別類型 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-3">類別類型</label>
                    <div class="flex bg-gray-100 rounded-xl p-1">
                        <button class="flex-1 py-3 rounded-lg text-center font-medium bg-white text-gray-900 shadow-sm">
                            <i class="fas fa-arrow-down mr-2 text-red-500"></i>支出
                        </button>
                        <button class="flex-1 py-3 rounded-lg text-center font-medium text-gray-500">
                            <i class="fas fa-arrow-up mr-2 text-green-500"></i>收入
                        </button>
                    </div>
                </div>

                <!-- 圖示選擇 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-3">選擇圖示</label>
                    <div class="grid grid-cols-6 gap-3">
                        <div class="icon-option selected flex items-center justify-center">
                            <i class="fas fa-utensils text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-car text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-shopping-bag text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-gamepad text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-home text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-heart text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-book text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-plane text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-coffee text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-gas-pump text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-mobile-alt text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-dumbbell text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-cut text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-paw text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-gift text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-tshirt text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-tv text-gray-600"></i>
                        </div>
                        <div class="icon-option flex items-center justify-center">
                            <i class="fas fa-tools text-gray-600"></i>
                        </div>
                    </div>
                </div>

                <!-- 顏色選擇 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-3">選擇顏色</label>
                    <div class="grid grid-cols-8 gap-3">
                        <div class="color-option selected" style="background-color: #ef4444;"></div>
                        <div class="color-option" style="background-color: #f97316;"></div>
                        <div class="color-option" style="background-color: #eab308;"></div>
                        <div class="color-option" style="background-color: #22c55e;"></div>
                        <div class="color-option" style="background-color: #06b6d4;"></div>
                        <div class="color-option" style="background-color: #3b82f6;"></div>
                        <div class="color-option" style="background-color: #8b5cf6;"></div>
                        <div class="color-option" style="background-color: #ec4899;"></div>
                        <div class="color-option" style="background-color: #64748b;"></div>
                        <div class="color-option" style="background-color: #374151;"></div>
                        <div class="color-option" style="background-color: #991b1b;"></div>
                        <div class="color-option" style="background-color: #92400e;"></div>
                        <div class="color-option" style="background-color: #365314;"></div>
                        <div class="color-option" style="background-color: #0e7490;"></div>
                        <div class="color-option" style="background-color: #1e40af;"></div>
                        <div class="color-option" style="background-color: #6b21a8;"></div>
                    </div>
                </div>

                <!-- 排序 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2">排序位置</label>
                    <div class="bg-gray-50 rounded-xl p-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">排序位置</span>
                            <div class="flex items-center space-x-3">
                                <button class="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm">
                                    <i class="fas fa-minus text-gray-400"></i>
                                </button>
                                <span class="text-lg font-semibold w-8 text-center">6</span>
                                <button class="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm">
                                    <i class="fas fa-plus text-gray-400"></i>
                                </button>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-2">較小的數字會顯示在前面</p>
                    </div>
                </div>

                <!-- 備註 -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-medium mb-2">備註 (選填)</label>
                    <textarea placeholder="添加類別描述或備註..." 
                              class="w-full bg-white border border-gray-200 rounded-xl px-4 py-3 h-20 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-4">
                <div class="flex space-x-3">
                    <button class="flex-1 bg-gray-100 text-gray-600 py-3 rounded-xl font-medium">
                        取消
                    </button>
                    <button class="flex-1 bg-blue-500 text-white py-3 rounded-xl font-medium">
                        儲存類別
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 